#!/bin/bash

# Chotot House Rent Crawler Deployment Script
# This script automates the deployment process for the crawler application

set -e

# Configuration
APP_NAME="chotot-house-rent-crawler"
APP_DIR="/home/<USER>/crawler-notifier"
REPO_URL="https://github.com/your-username/chotot-house-rent.git"
BRANCH="main"
USER="crawler"
GROUP="crawler"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if running as root
is_root() {
    [ "$EUID" -eq 0 ]
}

# System setup functions
install_system_dependencies() {
    log_info "Installing system dependencies..."

    if command_exists apt-get; then
        # Ubuntu/Debian
        apt-get update
        apt-get install -y \
            curl \
            wget \
            git \
            build-essential \
            python3 \
            python3-pip \
            ca-certificates \
            gnupg \
            lsb-release \
            software-properties-common
    elif command_exists yum; then
        # CentOS/RHEL
        yum update -y
        yum install -y \
            curl \
            wget \
            git \
            gcc \
            gcc-c++ \
            make \
            python3 \
            python3-pip \
            ca-certificates
    else
        log_error "Unsupported package manager. Please install dependencies manually."
        exit 1
    fi
    
    log_success "System dependencies installed"
}

install_puppeteer_dependencies() {
    log_info "Installing Puppeteer/Chrome dependencies..."

    if command_exists apt-get; then
        # Ubuntu/Debian - Install Chrome/Puppeteer dependencies
        apt-get install -y \
            ca-certificates \
            fonts-liberation \
            libasound2 \
            libatk-bridge2.0-0 \
            libatk1.0-0 \
            libc6 \
            libcairo2 \
            libcups2 \
            libdbus-1-3 \
            libexpat1 \
            libfontconfig1 \
            libgbm1 \
            libgcc1 \
            libglib2.0-0 \
            libgtk-3-0 \
            libnspr4 \
            libnss3 \
            libpango-1.0-0 \
            libpangocairo-1.0-0 \
            libstdc++6 \
            libx11-6 \
            libx11-xcb1 \
            libxcb1 \
            libxcomposite1 \
            libxcursor1 \
            libxdamage1 \
            libxext6 \
            libxfixes3 \
            libxi6 \
            libxrandr2 \
            libxrender1 \
            libxss1 \
            libxtst6 \
            lsb-release \
            wget \
            xdg-utils
    elif command_exists yum; then
        # CentOS/RHEL - Install equivalent packages
        yum install -y \
            alsa-lib \
            atk \
            cairo \
            cups-libs \
            dbus-glib \
            expat \
            fontconfig \
            GConf2 \
            glib2 \
            gtk3 \
            libdrm \
            libX11 \
            libX11-xcb \
            libxcb \
            libXcomposite \
            libXcursor \
            libXdamage \
            libXext \
            libXfixes \
            libXi \
            libXrandr \
            libXrender \
            libXScrnSaver \
            libXtst \
            nss \
            pango \
            redhat-lsb-core \
            wget \
            xdg-utils
    fi

    log_success "Puppeteer/Chrome dependencies installed"
}

install_nodejs() {
    log_info "Installing Node.js..."
    
    # Install Node.js 18.x
    if command_exists apt-get; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
        apt-get install -y nodejs
    elif command_exists yum; then
        curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
        yum install -y nodejs
    fi
    
    # Verify installation
    node_version=$(node --version)
    npm_version=$(npm --version)
    log_success "Node.js $node_version and npm $npm_version installed"
}

install_docker() {
    log_info "Installing Docker..."
    
    if command_exists apt-get; then
        # Ubuntu/Debian
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
        echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
        apt-get update
        apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    elif command_exists yum; then
        # CentOS/RHEL
        yum install -y yum-utils
        yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    fi
    
    # Start and enable Docker
    systemctl start docker
    systemctl enable docker
    
    # Add user to docker group
    usermod -aG docker "$USER" || true
    
    log_success "Docker installed and configured"
}

create_user() {
    log_info "Creating application user..."
    
    # Create user if it doesn't exist
    if ! id "$USER" &>/dev/null; then
        useradd -r -s /bin/bash -d "$APP_DIR" "$USER"
        log_success "User $USER created"
    else
        log_info "User $USER already exists"
    fi
    
    # Create application directory
    mkdir -p "$APP_DIR"
    chown -R "$USER:$GROUP" "$APP_DIR"
    
    # Create necessary subdirectories
    sudo -u "$USER" mkdir -p "$APP_DIR"/{data,output,logs,sessions}
}

# Application deployment functions
clone_repository() {
    log_info "Cloning repository..."
    
    if [ -d "$APP_DIR/.git" ]; then
        log_info "Repository already exists, pulling latest changes..."
        cd "$APP_DIR"
        sudo -u "$USER" git fetch origin
        sudo -u "$USER" git reset --hard "origin/$BRANCH"
    else
        sudo -u "$USER" git clone "$REPO_URL" "$APP_DIR"
        cd "$APP_DIR"
        sudo -u "$USER" git checkout "$BRANCH"
    fi
    
    log_success "Repository cloned/updated"
}

install_dependencies() {
    log_info "Installing application dependencies..."
    
    cd "$APP_DIR"
    sudo -u "$USER" npm ci --only=production
    
    log_success "Dependencies installed"
}

setup_configuration() {
    log_info "Setting up configuration..."
    
    cd "$APP_DIR"
    
    # Copy example configuration files if they don't exist
    if [ ! -f ".env" ]; then
        sudo -u "$USER" cp .env.example .env
        log_warning "Please edit .env file with your configuration"
    fi
    
    if [ ! -f "urls.json" ]; then
        sudo -u "$USER" cp urls.example.json urls.json
        log_info "URLs configuration copied"
    fi
    
    if [ ! -f "proxy.config.js" ]; then
        sudo -u "$USER" cp config/examples/proxy.config.example.js proxy.config.js
        log_info "Proxy configuration copied"
    fi

    if [ ! -f "captcha.config.js" ]; then
        sudo -u "$USER" cp config/examples/captcha.config.example.js captcha.config.js
        log_info "CAPTCHA configuration copied"
    fi
    
    log_success "Configuration files set up"
}

# Deployment modes
deploy_docker() {
    log_info "Deploying with Docker..."
    
    cd "$APP_DIR"
    
    # Build Docker image
    docker build -f deployment/docker/Dockerfile -t "$APP_NAME" .

    # Stop existing container if running
    docker stop "$APP_NAME" 2>/dev/null || true
    docker rm "$APP_NAME" 2>/dev/null || true

    # Start new container
    docker-compose -f deployment/docker/docker-compose.yml up -d
    
    log_success "Application deployed with Docker"
}

deploy_native() {
    log_info "Deploying natively..."
    
    # Install PM2 globally if not present
    if ! command_exists pm2; then
        npm install -g pm2
    fi
    
    cd "$APP_DIR"
    
    # Start application with PM2
    sudo -u "$USER" pm2 start deployment/ecosystem.config.js --env production
    sudo -u "$USER" pm2 save
    
    # Setup PM2 startup script
    pm2 startup
    
    log_success "Application deployed natively with PM2"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    # Wait for application to start
    sleep 10
    
    # Check if process is running
    if pgrep -f "node.*app/index.js" > /dev/null; then
        log_success "Application is running"
        return 0
    else
        log_error "Application is not running"
        return 1
    fi
}

# Backup functions
backup_data() {
    log_info "Creating backup..."
    
    backup_dir="/var/backups/chotot-crawler"
    backup_file="$backup_dir/backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    mkdir -p "$backup_dir"
    
    cd "$APP_DIR"
    tar -czf "$backup_file" data/ output/ sessions/ .env urls.json proxy.config.js captcha.config.js
    
    log_success "Backup created: $backup_file"
}

# Main deployment function
main() {
    local mode="$1"
    
    if [ -z "$mode" ]; then
        echo "Usage: $0 [docker|native|setup]"
        echo ""
        echo "Modes:"
        echo "  setup   - Initial server setup (install dependencies)"
        echo "  docker  - Deploy using Docker"
        echo "  native  - Deploy natively with PM2"
        exit 1
    fi
    
    # Check if running as root
    if ! is_root; then
        log_error "This script must be run as root"
        exit 1
    fi
    
    case "$mode" in
        setup)
            log_info "Starting initial server setup..."
            install_system_dependencies
            install_puppeteer_dependencies
            install_nodejs
            install_docker
            create_user
            log_success "Server setup completed"
            ;;
        docker)
            log_info "Starting Docker deployment..."
            clone_repository
            setup_configuration
            deploy_docker
            health_check
            log_success "Docker deployment completed"
            ;;
        native)
            log_info "Starting native deployment..."
            clone_repository
            install_dependencies
            setup_configuration
            deploy_native
            health_check
            log_success "Native deployment completed"
            ;;
        *)
            log_error "Unknown deployment mode: $mode"
            exit 1
            ;;
    esac
}

# Update function
update_application() {
    log_info "Updating application..."

    # Create backup before update
    backup_data

    # Pull latest changes
    clone_repository

    # Update dependencies
    cd "$APP_DIR"
    sudo -u "$USER" npm ci --only=production

    # Restart application
    if command_exists docker; then
        docker-compose -f deployment/docker/docker-compose.yml restart
    elif command_exists pm2; then
        sudo -u "$USER" pm2 restart deployment/ecosystem.config.js
    fi

    # Health check
    health_check

    log_success "Application updated successfully"
}

# Run main function
case "$1" in
    update)
        update_application
        ;;
    *)
        main "$@"
        ;;
esac
